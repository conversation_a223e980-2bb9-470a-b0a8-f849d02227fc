@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap");

.main {
    background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
    min-height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    font-family: poppins;
    /* position: relative; */
}

/* hamMenu */
.hamMenu {
    display: none;

    @media screen and (max-width : 576px) {
        display: flex;
        width: 100%;
        background-color: #4153ED;
        position: fixed;
        top: 0;
        height: 60px;
        z-index: 999;
        justify-content: space-between;
        align-items: center;
        padding: 0px 30px;
    }
}

/* Mobile header items */
.hamMenuLeft {
    display: none;

    @media screen and (max-width : 576px) {
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: opacity 0.2s ease;
        padding: 8px 12px;
        border-radius: 6px;
    }
}

.hamMenuLeft:hover {
    @media screen and (max-width : 576px) {
        opacity: 0.8;
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.hamMenuCenter {
    display: none;

    @media screen and (max-width : 576px) {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
    }
}

.hamMenuRight {
    display: none;

    @media screen and (max-width : 576px) {
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: opacity 0.2s ease;
        padding: 8px 12px;
        border-radius: 6px;
    }
}

.hamMenuRight:hover {
    @media screen and (max-width : 576px) {
        opacity: 0.8;
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.leftContainerWrapper {
    width: 20%;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 100;
    padding: 30px 10px;

    @media screen and (max-width : 576px) {
        /* display: none; */
        width: 80% !important;
        position: absolute;
        background-color: #4153ED;
        height: 100%;
        transition: 0.4s linear;
        padding: 30px 10px;
    }
}

.leftContainerWrapperHide {
    width: 20%;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 100;
    padding: 30px 10px;

    @media screen and (max-width : 576px) {
        /* display: none; */
        width: 80% !important;
        position: absolute;
        background-color: #4153ED;
        height: 100%;
        translate: -350px;
        transition: 0.1s linear;
        padding: 30px 10px;
    }
}

/* hamMenu */

.wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
    margin: auto;
    justify-content: space-between;

    @media screen and (max-width : 576px) {
        /* display: none; */
        width: 100%;
        height: 100vh;
    }
}



.leftContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;

    @media screen and (max-width : 576px) {
        /* display: none; */
        width: 100%;
        /* position: absolute; */
        background-color: #4153ED;
        height: 100%;
    }
}

/* Logo area styles moved to sidebar component */

.sidebar {
    width: 100%;
    height: auto;
    border-radius: 15px;
    background: #fff;


}

.pages {
    margin-top: 30px;

    width: 100%;
    height: 100%;
}

.dashboardContainer {
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
    border-bottom: 1px solid #ececec;
}

.historyContainer {
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
}

.logoutContainer {
    /* margin-bottom: auto; */
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
    /* border-bottom: 1px solid #ECECEC; */
}

.sideIcons {
    margin-left: 20px;
    margin-right: 10px;
    color: #4153ed;
}

.dashboard {
    color: #4153ed;
}

.rightContainer {
    width: 75%;
    height: 100vh;
    display: flex;
    margin-left: 22%;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-left: 0;
    }

}

/* Hide scrollbar for webkit browsers */
.rightContainer::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for Firefox */
.rightContainer {
    scrollbar-width: none;
}

.rightContainerWrapper {
    width: 100%;
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    padding: 30px 10px;
    min-height: 100vh;
}

.rightContainerHeader {
    /* width: 100%; */
    height: 94px;
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;

    @media screen and (max-width : 576px) {
        display: none;
    }
}

.rightContainerBody {
    min-height: calc(100vh - 94px);
    padding: 30px;
    border-radius: 20px;
    background: #fff;
    margin-bottom: 20px;

    @media screen and (max-width : 576px) {
        padding: 15px;
        min-height: calc(100vh - 140px);
    }
}

.body {
    height: 100%;
    background-color: #fff;
    width: 100%;
}

.firstformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.secondformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    margin-top: 30px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.thirdformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 30px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.firstName {
    width: 46%;
    height: 40px;

    @media screen and (max-width: 576px) {
        width: 100%;
        margin: 40px 0px;
    }
}

.firstNameLabel {
    font-size: 12px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
        margin-left: 17px;
    }
}

.sec_firstName {
    width: 20%;
    height: 40px;

    @media screen and (max-width: 576px) {
        width: 100%;
        margin-bottom: 50px;
    }
}

.firstNameLabel {
    font-size: 12px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
    }
}

.firstNameInput {
    display: flex;

    @media screen and (max-width: 576px) {
        padding: 15px;
    }
}

.firstNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    height: 40px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
        padding: none;
    }
}

.firstNameInput select {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    height: 40px;
    padding-right: 10px;
}

.emailBtn {
    border-radius: 0px 2px 2px 0px;
    background: #f5f5f5;
    border: 1px solid #ebebeb;
    border-left: 2px solid #c4c3c3;
    font-size: 10px;
    font-weight: 600;
    width: 57px;
}

.addressName {
    width: 100%;
    margin-left: 23px;
}

.addressNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 96%;

    height: 40px;
    padding-left: 10px;
    padding-right: 10px;

    @media screen and (max-width: 576px) {
        width: 87%;

    }
}

.fourthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 55px;
}

.paymentBoxContainer {
    width: 60%;
    height: auto;
    border-radius: 15px;
    border: 1px solid #d9d9d9;
    background: #fff;
    margin: auto;

}

.paymentBox {
    background-color: #4f535a;
    width: 100%;
    /* padding: 25px 20px; */

}

.paymentHeader {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding: 0px 15px;
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
}

.paymentDirection {
    padding: 0px 15px;
    /* background-color: #4153ed; */
    color: #000;
    text-align: center;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payFrom {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid black;
}

.payTo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid #efefef;
}

.fifthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: auto;
    margin-bottom: 20px;
    /* margin-top: 55px; */
}

.paymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.BitcoinpaymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.listing_BtnCont {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}

.listing_Btn {
    width: 365px;
    height: 50px;
    border-radius: 5px;
    border: 2px solid #4153ED;
    background: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #4153ED;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;

    @media screen and (max-width: 576px) {
        margin-bottom: 30px;

    }
}

.rightContainerBox {
    display: flex;
    justify-content: space-between;

}

.rightContainerBell {
    margin-right: 50px;
    display: flex;
    justify-content: center;
    position: relative;
    transition: 1s linear;
}


.icon_button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    color: #333333;
    background: #dddddd;
    border: none;
    outline: none;
    border-radius: 50%;
    cursor: pointer;
}

.icon-button:hover {
    cursor: pointer;
}

.icon-button:active {
    background: #cccccc;
}

.icon_button__badge {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 25px;
    background: red;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}

.notificationLists {
    position: absolute;
    height: 300px;
    width: 300px;
    background-color: #4153ED;
    top: 40px;
    right: 0px;
    border-radius: 12px;
    z-index: 1000;
    transition: height 1s linear, opacity 1s linear;
    opacity: 1;
    overflow: hidden;

    @media screen and (max-width: 576px) {
        z-index: 99999;
    }
}

.overlayMsg {
    position: fixed;
    right: 10px;
    bottom: 30px;
    width: 300px;
    height: 100px;
    background-color: #1448d6fd;
    border-radius: 5px;
    color: #f3eded;

    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    padding: 30px 20px;
}

.cancelTimerBtn {
    margin-top: 10px;
    border: none;
    padding: 5px 10px;
    font-family: poppins;
    font-weight: 700;
    border-radius: 5px;
    width: 100px;
    background-color: #f3eded;
    cursor: pointer;
}

/* Desktop Title Wrapper */
.desktopTitleWrapper {
    @media screen and (max-width: 576px) {
        display: none;
    }
}

/* Notification Wrapper */
.notificationWrapper {
    @media screen and (max-width: 576px) {
        position: fixed;
        top: 15px;
        right: 15px;
        z-index: 1000;
    }
}