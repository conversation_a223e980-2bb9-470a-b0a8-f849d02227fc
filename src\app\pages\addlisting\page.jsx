"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./addlisting.module.css";
import PaymentMethod from "../../components/paymentMethod/page";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Layout from "../../components/Layout/page";
import {
  useFxRate,
  useFinalTradeFee,
  useCurrencyFromData,
  useCurrencyToData,
  usePaymentMethodsAccepted,
} from "@/app/hooks/addListinghooks";
import getCheckMin from "@/app/api/addListing/checkValidMinimumAmount";
import { debounce } from "lodash";
import { useRouter } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = () => {
  const router = useRouter();
  const [hide, setHide] = useState(false);
  const [currencyAccepted, setCurrencyAccepted] = useState("");
  const [currencyPayout, setCurrencyPayout] = useState("");
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [loadCurrencyTo, setLoadCurrencyTo] = useState([]);
  const [liquidity, setLiquidity] = useState("");
  const [minTradeLimit, setMinTradeLimit] = useState("");
  const [maxTradeLimit, setMaxTradeLimit] = useState("");
  const [tradeFee, setTradeFee] = useState("");
  const [finalTradeFee, setFinalTradeFee] = useState("");
  const [termsandConditions, setTermsandConditions] = useState("");
  const [fxRate, setFxRate] = useState("");
  const [messageAccepted, setMessageAccepted] = useState("");
  const [messagePayout, setMessagePayout] = useState("");
  const [dataAccepted, setDataAccepted] = useState([]);
  const [dataPayout, setDataPayout] = useState([]);
  const [dataFromChildAccepted, setDataFromChildAccepted] = useState("");
  const [dataFromChildPayout, setDataFromChildPayout] = useState("");
  const [userPayData, setUserPayData] = useState([]);
  const [availableAccData, setAvailableAccData] = useState([]);
  const [availableAccDataMessage, setAvailableAccDataMessage] = useState("");
  const [selectedAvailableAccId, setSelectedAvailableAccId] = useState("");
  const [selectedTimeLimit, setSelectedTimeLimit] = useState("-1");

  const authTokenRef = useRef(null);
  let token;
  if (typeof window !== "undefined") {
    token = localStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }

  const handleCurrencyChangeAccepted = (event) => {
    setCurrencyAccepted(event.target.value);
  };
  const handleCurrencyChangePayout = (event) => {
    setCurrencyPayout(event.target.value);
  };

  const loadCurrencyFromResult = useCurrencyFromData();
  const loadCurrencyToResult = useCurrencyToData();

  const fxRateResult = useFxRate(currencyAccepted, currencyPayout);

  // negative validation checks for inputs and handle set functions
  const handleSetAvailableTradeLimit = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");
    if (inputValue < 1 || inputValue.length > 10) {
      setLiquidity("");
    } else {
      setLiquidity(inputValue);
    }
  };
  const checkMin = async () => {
    if (currencyAccepted.length < 1 && minTradeLimit.length < 1) {
      return null;
    }
    try {
      const res = await getCheckMin(currencyAccepted, minTradeLimit);

      if (res.data.status == 200) {
        toast.error(res.data.message);
        setMinTradeLimit("");
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    const debouncedCheckMin = debounce(checkMin, 2000);
    debouncedCheckMin();
    return () => {
      debouncedCheckMin.cancel(); // Cancel the debounced function on unmount
    };
  }, [minTradeLimit]);

  const handleSetMinTradeLimit = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");

    if (inputValue < 1 || inputValue.length > 10) {
      setMinTradeLimit("");
    } else {
      setMinTradeLimit(inputValue);
    }
  };

  const handleSetMaxTradeLimit = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");

    if (inputValue < 1 || inputValue.length > 10) {
      setMaxTradeLimit("");
    } else {
      setMaxTradeLimit(inputValue);
    }
  };

  const handleSetTradeFee = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");

    if (inputValue > 100 || inputValue.length > 20) {
      setTradeFee("100");
    } else {
      setTradeFee(inputValue);
    }
  };

  const handleTermsandConditions = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");

    if (inputValue.length > 200) {
      setTermsandConditions("");
    } else {
      setTermsandConditions(inputValue);
    }
  };

  // const handleSelectedSavedAcc = (e) => {
  //   const value = e.target.value;
  //   const paymentMethodAcc = value.split(" - ")[1];

  //   const filteredData = availableAccData.find((el) =>
  //     el.data.some((item) => item.value === paymentMethodAcc)
  //   );

  //   if (filteredData) {
  //     setSelectedAvailableAccId(filteredData.id);
  //   } else {
  //     (selectedValue === "no-accounts") {
  //       window.location.href = "/pages/accounts";
  //     }
  //   }
  // };
  const handleSelectedSavedAcc = (e) => {
    const value = e.target.value;

    // Check for the "no-accounts" option first
    if (value === "no-accounts") {
      router.push("/pages/accounts");
      return;
    }
    const paymentMethodAcc = value.split(" - ")[1];

    const filteredData = availableAccData.find((el) =>
      el.data.some((item) => item.value === paymentMethodAcc)
    );

    if (filteredData) {
      setSelectedAvailableAccId(filteredData.id);
    }
  };

  // negative validation checks for inputs and handle set functions
  //get final trade fee

  const fetchFinalTradeFee = async () => {
    if (!tradeFee || !fxRate) {
      return;
    }
    try {
      const resFx = await fetch(
        `${BaseURL}/final-exchange-rate/?trade_fee=${tradeFee}&indicative_fx_rate=${fxRate}`
      );
      const data = await resFx.json();
      if (!resFx.ok) {
        setFinalTradeFee("enter trade fee % for rate");
      } else setFinalTradeFee(Number(data.data.final_exchange_rate));
    } catch (error) {
      console.log(error);
    }
  };

  //get final trade fee

  //get Payment methods

  // const { dataAcceptedRes, messageAcceptedRes } =
  //   usePaymentMethodsAccepted(currencyAccepted);

  const fetchPaymentMethodsAccepted = async () => {
    if (currencyAccepted.length < 1) {
      return;
    }
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyAccepted}`
      );
      // const resCurrency = await fetch(`${BaseURL}/payment-list/?currency=INR`);
      const data = await resCurrency.json();

      // setRenderCount((prevCount) => prevCount + 1);

      setDataAccepted(data.data);
      setMessageAccepted(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchPaymentMethodspayout = async () => {
    if (currencyPayout.length < 1) {
      return;
    }
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyPayout}`
      );
      // const resCurrency = await fetch(`${BaseURL}/payment-list/?currency=INR`);
      const data = await resCurrency.json();

      setDataPayout(data.data);
      setMessagePayout(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  //get Payment methods

  const handleDataFromChildAccepted = (selectedAcceptedPaymentData) => {
    // Do something with the data in the parent component
    setDataFromChildAccepted(selectedAcceptedPaymentData);
  };

  const handleDataFromChildPayout = (selectedPayoutPaymentData) => {
    // Do something with the data in the parent component
    setDataFromChildPayout(selectedPayoutPaymentData);
  };
  //get Payment methods

  //aavailable Accounts

  const getAvailableAccounts = async () => {
    if (dataFromChildAccepted.length < 1) {
      return null;
    }
    try {
      const res = await customFetchWithToken.get(
        `/get-user-choice-payment-fields-data/?payment_method=${dataFromChildAccepted}`
      );

      setAvailableAccDataMessage(res.data.message);

      setAvailableAccData(res.data.data);
    } catch (error) {
      console.log(error);
      console.log("niftyErr", error.response.data.message);
      setAvailableAccDataMessage(error.response.data.message);
    }
  };

  //aavailable Accounts

  useEffect(() => {
    const userPaymentData = async () => {
      const res = await customFetchWithToken.get("/user-payment-fields-data/");

      setUserPayData(res.data.data);
    };
    userPaymentData();
  }, []);

  useEffect(() => {
    setLoadCurrencyFrom(loadCurrencyFromResult);
  }, [loadCurrencyFromResult]);
  useEffect(() => {
    setLoadCurrencyTo(loadCurrencyToResult);
  }, [loadCurrencyToResult]);

  useEffect(() => {
    setFxRate(fxRateResult);
  }, [fxRateResult]);

  useEffect(() => {
    fetchFinalTradeFee();
  }, [tradeFee]);

  useEffect(() => {
    fetchPaymentMethodspayout();
  }, [currencyPayout]);

  useEffect(() => {
    fetchPaymentMethodsAccepted();
  }, [currencyAccepted]);

  useEffect(() => {
    getAvailableAccounts();
  }, [dataFromChildAccepted]);

  // useEffect(() => {
  //   // Update the state or perform other actions with dataAccepted and messageAccepted
  //   setDataAccepted(dataAcceptedRes);
  //   setMessageAccepted(messageAcceptedRes);
  // }, [currencyAccepted]);

  const Data = {
    currency_accepted: currencyAccepted,
    currency_payout: currencyPayout,
    available_liquidity: Number(liquidity),
    terms_and_conditions: termsandConditions,
    min_liquidity: Number(minTradeLimit),
    max_liquidity: Number(maxTradeLimit),
    indicative_fx_rate: fxRate,
    payin_option: dataFromChildAccepted,
    payout_option: dataFromChildPayout,
    trade_fee: Number(tradeFee),
    user_payment_option: selectedAvailableAccId,
    time_limit: Number(selectedTimeLimit),
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const URL = `${BaseURL}/add-listings/`;

  const addListingHandler = async (e) => {
    e.preventDefault();

    if (currencyAccepted.length == 0) {
      return toast.error("Please enter accepted currencies");
    }

    if (currencyPayout.length == 0) {
      return toast.error("Please enter payout currencies");
    }

    if (liquidity.length == 0) {
      return toast.error("Please enter a liquidity value");
    }

    if (minTradeLimit.length == 0) {
      return toast.error("Please enter a minimum trade limit");
    }

    if (maxTradeLimit.length == 0) {
      return toast.error("Please enter a maximum trade limit");
    }

    if (finalTradeFee.length == 0) {
      return toast.error("Please enter a final trade fee");
    }

    if (tradeFee.length == 0) {
      return toast.error("Please enter a trade fee");
    }
    if (selectedTimeLimit == null) {
      return toast.error("Please enter a selected time limit");
    }

    if (termsandConditions.length == 0) {
      return toast.error("Please enter the terms and conditions");
    }

    try {
      const res = await customFetchWithToken.post("/add-listings/", Data);

      if (res.status === 200 || res.status === 201) {
        toast.success(res.data.message);
        setDataAccepted([]);
        setLiquidity("");
        setMinTradeLimit("");
        setMaxTradeLimit("");
        setFxRate("");
        setTradeFee("");
        setFinalTradeFee("");
        setTermsandConditions("");
        setAvailableAccData([]);
        setSelectedTimeLimit("-1");
        setMessageAccepted("");
        setMessagePayout("");
        setCurrencyAccepted("");
        setCurrencyPayout("");
      } else {
        toast.error("Add Listing Failed.");
      }
    } catch (error) {
      toast.error(error.response.data.message);
      console.log(error);
    }
  };
  const hideHam = () => {
    setHide(!hide);
  };
  // Call checkMin with a delay after minTradeLimit is updated
  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     checkMin();
  //   }, 1000);

  //   return () => clearTimeout(timer); // Clear the timeout if the component unmounts or the dependency changes
  // }, [minTradeLimit]);

  const addListingTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Add Listing</h1>
      <p className={styles.pageSubtitle}>
        Create a new listing to start trading • {new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
      </p>
    </div>
  );

  return (
    <>
      {/* {authTokenRef.current ? ( */}
      <div>
        <Layout title={addListingTitle}>
          <div className={styles.rightContainerBody}>
            <div className={styles.body}>
              {/* Header Section - Hidden on desktop, shown only on mobile */}
              <div className={styles.addListingHeader}>
                <div className={styles.headerContent}>
                  <h1 className={styles.pageTitle}>Add Listing</h1>
                  <p className={styles.pageSubtitle}>
                    Create a new listing to start trading • {new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                  </p>
                </div>
              </div>
              <div className={styles.firstformWrapper}>
                <div className={styles.firstName}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="currencyAccepted">Currency accepted</label>
                  </div>
                  <div className={styles.firstNameInput}>
                    <select
                      name="currencyAccepted"
                      id="currencyAccepted"
                      value={currencyAccepted}
                      onChange={handleCurrencyChangeAccepted}
                    >
                      <option value="-1">Please select a currency</option>
                      {loadCurrencyFrom?.map((currency) => (
                        <option
                          key={currency.id}
                          value={currency.currency_code}
                        >
                          {currency.currency_code}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className={styles.firstName}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="currencyPayout">Currency of payout</label>
                  </div>
                  <div className={styles.firstNameInput}>
                    <select
                      name="currencyPayout"
                      id="currencyPayout"
                      value={currencyPayout}
                      onChange={handleCurrencyChangePayout}
                    >
                      <option value="-1">Please select a currency</option>
                      {loadCurrencyTo.map((currency) => (
                        <option
                          key={currency.id}
                          value={currency.currency_code}
                        >
                          {currency.currency_code}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              {/* Trading Limits Section - Modified for single line layout */}
              <div className={styles.tradingLimitsWrapper}>
                <div className={styles.tradingLimitItem}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="liquidity_available">
                      Liquidity Available
                    </label>
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      style={{
                        WebkitAppearance: "none",
                        margin: 0,
                      }}
                      type="text"
                      id="liquidity_available"
                      placeholder="enter liquidity available"
                      min="0"
                      onChange={handleSetAvailableTradeLimit}
                      value={liquidity}
                      required
                    />
                  </div>
                </div>
                <div className={styles.tradingLimitItem}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="Minimum_trade_limit">
                      Minimum trade limit
                    </label>
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="Minimum_trade_limit"
                      placeholder="enter minimum liquidity"
                      min="0"
                      pattern="\d*"
                      maxLength="20"
                      onChange={handleSetMinTradeLimit}
                      value={minTradeLimit}
                      required
                    />
                  </div>
                </div>
                <div className={styles.tradingLimitItem}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="Maximum_trade_limit">
                      Maximum trade limit
                    </label>
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="Maximum_trade_limit"
                      placeholder="enter maximum liquidity"
                      value={maxTradeLimit}
                      pattern="\d*"
                      maxLength="20"
                      onChange={handleSetMaxTradeLimit}
                      required
                    />
                  </div>
                </div>
              </div>
              
              {/* FX Rate, Listing Margin, Final Rate Section */}
              <div className={styles.tradingLimitsWrapper}>
                <div className={styles.tradingLimitItem}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="Indicative">Official FX Rate</label>
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="Indicative"
                      value={fxRate ? Number(fxRate).toFixed(2) : ""}
                      readOnly
                      required
                    />
                  </div>
                </div>
                <div className={styles.tradingLimitItem}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="Trade">% Listing Margin</label>{" "}
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="Trade"
                      placeholder="enter trade fee percentage"
                      pattern="\d*"
                      maxLength="20"
                      value={tradeFee}
                      onChange={handleSetTradeFee}
                      required
                    />
                  </div>
                </div>
                <div className={styles.tradingLimitItem}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="finalTrade">Final Rate</label>{" "}
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="finalTrade"
                      value={finalTradeFee}
                      pattern="\d*"
                      maxLength="20"
                      readOnly
                      onChange={(e) => setFinalTradeFee(e.target.value)}
                      required
                    />
                  </div>
                </div>
              </div>

              <div className={styles.fourthformWrapper}>
                <PaymentMethod
                  PmessageAccepted={messageAccepted}
                  PmessagePayout={messagePayout}
                  PcurrencyTo={currencyPayout}
                  PcurrencyFrom={currencyAccepted}
                  PaymentMdataAccepted={dataAccepted}
                  PaymentMdataPayout={dataPayout}
                  onDataFromChildAccepted={handleDataFromChildAccepted}
                  onDataFromChildPayout={handleDataFromChildPayout}
                />
              </div>

              <div className={styles.thirdformWrapper}>
                {!Array.isArray(dataAccepted) ||
                dataAccepted.some(
                  (item) => item.payment_method !== "Crypto"
                ) ? (
                  <div className={styles.addressName}>
                    <div className={styles.firstNameLabel}>
                      <label htmlFor="finalTrade">Available Accounts </label>{" "}
                    </div>
                    <div className={styles.addressNameInput1}>
                      <select
                        className={styles.addressNameSelect}
                        onChange={handleSelectedSavedAcc}
                      >
                        <option value="-1">
                          {availableAccDataMessage === "Data found."
                            ? "Select available Payment Method"
                            : "You Don't have any Saved Accounts"}
                        </option>
                        {availableAccDataMessage !== "Data found." && (
                          <option value="no-accounts">
                            Go to Saved Accounts page to Create an account to
                            Complete the Listing
                          </option>
                        )}
                        {availableAccDataMessage === "Data found." &&
                          availableAccData.map((item, index) => (
                            <option key={index}>
                              {item.data[0].key} - {item.data[0].value}
                            </option>
                          ))}
                      </select>
                    </div>
                  </div>
                ) : null}

                <div className={styles.thirdformWrapper}>
                  <div className={styles.addressName}>
                    <div className={styles.firstNameLabel}>
                      <label htmlFor="finalTrade">Set Trade Time Limit</label>{" "}
                    </div>
                    <div className={styles.addressNameInput1}>
                      <select
                        className={styles.addressNameSelect}
                        value={selectedTimeLimit}
                        onChange={(e) => setSelectedTimeLimit(e.target.value)}
                      >
                        <option value="-1">Select Time Limit</option>
                        <option value="5">5 minutes</option>
                        <option value="10">10 minutes</option>
                        <option value="15">15 minutes</option>
                        <option value="20">20 minutes</option>
                        <option value="25">25 minutes</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div className={styles.thirdformWrapper}>
                <div className={styles.addressName}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="terms">
                      Terms and conditions (max 200 characters)
                    </label>{" "}
                  </div>
                  <div className={styles.addressNameInput}>
                    <textarea
                      className={styles.textAreaBox}
                      rows="4"
                      cols="50"
                      maxLength="200"
                      id="terms"
                      value={termsandConditions}
                      onChange={handleTermsandConditions}
                      required
                    ></textarea>
                  </div>
                </div>

              </div>

              <div className={styles.listing_BtnCont}>
                <button
                  className={styles.listing_Btn}
                  onClick={addListingHandler}
                >
                  Add Listing
                </button>
              </div>
            </div>
          </div>
        </Layout>
      </div>
      <ToastContainer />
      {/* ) : (
        <Login />
      )} */}
    </>
  );
};

export default page;
