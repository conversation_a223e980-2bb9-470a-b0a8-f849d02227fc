"use client";
import { useRef, useEffect, useState } from "react";
import Image from "next/image";
import styles from "./dashboard.module.css";
import Graph from "../../components/Dashboard/Graph/page";
import Layout from "../../components/Layout/page";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useTimer } from "@/app/context/TimerContext";

const Dashboard = () => {
  const router = useRouter();
  const [transactionData, setTransactionData] = useState([]);
  const [dashboardData, setDashboardData] = useState({
    total_transaction_volumes: "0",
    registered_users_count: "0",
    ad_listing_count: "0"
  });
  const [timeRange, setTimeRange] = useState("Daily");
  const [refCode, setRefCode] = useState("gsTSM8bAAy");
  const [copied, setCopied] = useState(false);
  const [balance, setBalance] = useState({ asset: "USDT", free: "11" });
  const [isLoading, setIsLoading] = useState(true);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const res = await customFetchWithToken.get("/dashboard/");
      setDashboardData(res.data);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = () => {
    setCopied(true);
    toast.success("Referral link copied to clipboard!");
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  return (
    <Layout title="Dashboard">
      <div className={styles.dashboardContainer}>
        {/* Header Section */}
        <div className={styles.dashboardHeader}>
          <div className={styles.headerContent}>
            <h1 className={styles.pageTitle}>Dashboard</h1>
            <p className={styles.pageSubtitle}>
              Overview of your account activity and balances • {new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>💰</div>
            <div className={styles.statLabel}>Total Transaction Volume</div>
            <div className={styles.statValue}>
              ${dashboardData.total_transaction_volumes}
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👥</div>
            <div className={styles.statLabel}>Total Completed Trades</div>
            <div className={styles.statValue}>120</div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>📝</div>
            <div className={styles.statLabel}>Total Active Listings</div>
            <div className={styles.statValue}>429</div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👤</div>
            <div className={styles.statLabel}>Total Users</div>
            <div className={styles.statValue}>94</div>
          </div>
        </div>

        {/* Transaction Overview */}
        <div className={styles.transactionOverview}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Transaction Overview</h2>
            <select 
              className={styles.periodSelect}
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <option value="Daily">Daily</option>
              <option value="Weekly">Weekly</option>
              <option value="Monthly">Monthly</option>
            </select>
          </div>

          <div className={styles.statsContainer}>
            <div className={styles.statItem}>
              <div className={styles.statItemLabel}>Total Transactions</div>
              <div className={styles.statItemValue}>120</div>
            </div>
            <div className={styles.statItem}>
              <div className={styles.statItemLabel}>Total Volume</div>
              <div className={styles.statItemValue}>$25,727</div>
            </div>
            <div className={styles.statItem}>
              <div className={styles.statItemLabel}>Average Amount</div>
              <div className={styles.statItemValue}>$214</div>
            </div>
          </div>

          <Graph />
        </div>

        {/* Wallet Balance */}
        <div className={styles.walletSection}>
          <div className={styles.walletHeader}>
            <h2 className={styles.walletTitle}>Wallet Balance</h2>
            <button className={styles.manageButton} onClick={() => router.push("/pages/remflowFunds")}>
              <span>Manage Wallet</span>
              <span>→</span>
            </button>
          </div>

          <div className={styles.walletCard}>
            <div className={styles.walletInfo}>
              <div className={styles.walletIcon}>
                <Image src="/assets/payments/tether.svg" alt="Tether USDT" width={28} height={28} />
              </div>
              <div className={styles.walletDetails}>
                <div className={styles.walletCurrency}>USDT</div>
                <div className={styles.walletType}>Tether</div>
              </div>
            </div>
            <div className={styles.walletBalance}>
              {isLoading ? (
                <div className={styles.loadingSpinner}></div>
              ) : (
                `${balance.free} ${balance.asset}`
              )}
            </div>
          </div>


        </div>

        {/* Currency Pairs */}
        <div className={styles.currencyPairsSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Top Currency Pairs</h2>
          </div>

          <div className={styles.pairsGrid}>
            <div className={styles.pairCard}>
              <div className={styles.pairHeader}>
                <div className={styles.currencyIcon}>GBP</div>
                <div className={styles.currencyName}>GBP/USDT</div>
              </div>
              <div className={styles.pairStats}>
                <span>198 trades</span>
                <span>$30,325</span>
              </div>
            </div>

            <div className={styles.pairCard}>
              <div className={styles.pairHeader}>
                <div className={styles.currencyIcon}>INR</div>
                <div className={styles.currencyName}>USDT/INR</div>
              </div>
              <div className={styles.pairStats}>
                <span>27 trades</span>
                <span>$30,326</span>
              </div>
            </div>
          </div>
        </div>

        {/* Referral Section */}
        <div className={styles.referralSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Invite Friends & Earn Rewards</h2>
          </div>

          <div className={styles.referralContent}>
            <p>Share your unique referral link with friends and earn rewards when they sign up and complete their first transaction on Remflow.</p>
            
            <div className={styles.referralCode}>
              <span className={styles.codeText}>{refCode}</span>
              <CopyToClipboard text={refCode} onCopy={handleCopy}>
                <button className={styles.copyButton}>Copy Referral Link</button>
              </CopyToClipboard>
            </div>

            <div className={styles.referralSteps}>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>1</div>
                <div className={styles.stepTitle}>Share Your Link</div>
                <div className={styles.stepDescription}>Send your code to friends</div>
              </div>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>2</div>
                <div className={styles.stepTitle}>Friends Sign Up</div>
                <div className={styles.stepDescription}>They create an account with your code</div>
              </div>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>3</div>
                <div className={styles.stepTitle}>Earn Rewards</div>
                <div className={styles.stepDescription}>Get bonuses for each referral</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer />
    </Layout>
  );
};

export default Dashboard;
