@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Modern History Page Styles */
.historyPageContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.contentWrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  min-height: calc(100vh - 120px);
}

.pageHeader {
  margin-bottom: 32px;
  text-align: center;
  display: none;
}

@media (max-width: 576px) {
  .pageHeader {
    display: block;
    margin-bottom: 20px;
    padding: 0 16px;
    text-align: center;
  }
}

.pageTitle {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
  letter-spacing: -0.025em;
}

.pageSubtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* Filters Section */
.filtersSection {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.searchControls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.searchInputGroup,
.statusFilterGroup {
  display: flex;
  flex-direction: column;
}

.searchLabel,
.filterLabel {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: block;
}

.searchInput,
.statusSelect {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background-color: #ffffff;
  transition: all 0.2s ease;
  outline: none;
}

.searchInput:focus,
.statusSelect:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.searchInput:hover,
.statusSelect:hover {
  border-color: #d1d5db;
}

.inputHelp {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

.actionButtons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #374151;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  min-width: 140px;
  justify-content: center;
}

.filterButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

.filterButton:active:not(:disabled) {
  transform: translateY(0);
}

.filterButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.allButton {
  border-color: #10b981;
  color: #10b981;
}

.allButton:hover:not(:disabled) {
  background: #10b981;
  color: #ffffff;
}

.peerButton {
  border-color: #3b82f6;
  color: #3b82f6;
}

.peerButton:hover:not(:disabled) {
  background: #3b82f6;
  color: #ffffff;
}

.senderButton {
  border-color: #f59e0b;
  color: #f59e0b;
}

.senderButton:hover:not(:disabled) {
  background: #f59e0b;
  color: #ffffff;
}

.buttonIcon {
  font-size: 16px;
}

/* Results Header */
.resultsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.resultsTitle {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.resultsCount {
  font-size: 14px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 500;
}

/* Error Message */
.errorMessage {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
}

.errorIcon {
  font-size: 18px;
}

/* Content Section */
.contentSection {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  min-height: 400px;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loadingText {
  margin-top: 16px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* Trades Container */
.tradesContainer {
  min-height: 300px;
}

.desktopTradesGrid {
  display: block;
}

.mobileTradesGrid {
  display: none;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.emptyStateIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.emptyStateTitle {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.emptyStateMessage {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 20px 0;
  max-width: 400px;
  line-height: 1.5;
}

.clearFiltersButton {
  padding: 10px 20px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clearFiltersButton:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Pagination Styles */
.paginationTop,
.paginationBottom {
  display: flex;
  justify-content: center;
  margin: 24px 0;
}

.paginationBottom {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.paginationContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.paginationPage,
.paginationPrev,
.paginationNext,
.paginationBreak {
  margin: 0;
}

.paginationLink,
.paginationPrevLink,
.paginationNextLink,
.paginationBreakLink {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #374151;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: #ffffff;
  min-width: 40px;
  height: 40px;
}

.paginationLink:hover,
.paginationPrevLink:hover,
.paginationNextLink:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #111827;
}

.paginationActive .paginationLink {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.paginationActive .paginationLink:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.paginationBreakLink {
  border: none;
  background: transparent;
  color: #9ca3af;
}

.paginationPrevLink,
.paginationNextLink {
  padding: 8px 16px;
}



/* Responsive Design */
@media (max-width: 1024px) {
  .contentWrapper {
    padding: 20px;
  }

  .searchControls {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .actionButtons {
    justify-content: stretch;
  }

  .filterButton {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .contentWrapper {
    padding: 16px;
  }

  .pageTitle {
    font-size: 28px;
  }

  .pageSubtitle {
    font-size: 14px;
  }

  .filtersSection {
    padding: 20px;
  }

  .contentSection {
    padding: 20px;
  }

  .desktopTradesGrid {
    display: none;
  }

  .mobileTradesGrid {
    display: block;
  }

  .actionButtons {
    flex-direction: column;
  }

  .filterButton {
    width: 100%;
    justify-content: center;
  }

  .resultsHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .resultsCount {
    align-self: flex-start;
  }

  .paginationContainer {
    flex-wrap: wrap;
    gap: 4px;
  }

  .paginationLink,
  .paginationPrevLink,
  .paginationNextLink {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 32px;
    height: 32px;
  }

  .paginationPrevLink,
  .paginationNextLink {
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .contentWrapper {
    padding: 12px;
  }

  .pageTitle {
    font-size: 24px;
  }

  .filtersSection,
  .contentSection {
    padding: 16px;
  }

  .searchInput,
  .statusSelect {
    padding: 10px 12px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .filterButton {
    padding: 10px 16px;
    font-size: 13px;
  }

  .emptyStateIcon {
    font-size: 40px;
  }

  .emptyStateTitle {
    font-size: 18px;
  }

  .emptyStateMessage {
    font-size: 13px;
  }
}

/* Focus and accessibility improvements */
.searchInput:focus-visible,
.statusSelect:focus-visible,
.filterButton:focus-visible,
.clearFiltersButton:focus-visible,
.paginationLink:focus-visible,
.paginationPrevLink:focus-visible,
.paginationNextLink:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .filtersSection,
  .contentSection,
  .resultsHeader {
    border-width: 2px;
  }

  .searchInput,
  .statusSelect {
    border-width: 2px;
  }

  .filterButton {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .filterButton,
  .clearFiltersButton,
  .paginationLink,
  .paginationPrevLink,
  .paginationNextLink {
    transition: none;
  }

  .filterButton:hover:not(:disabled) {
    transform: none;
  }

  .clearFiltersButton:hover {
    transform: none;
  }
}

/* Print styles */
@media print {
  .filtersSection,
  .paginationTop,
  .paginationBottom {
    display: none;
  }

  .contentSection {
    box-shadow: none;
    border: 1px solid #000;
  }
}