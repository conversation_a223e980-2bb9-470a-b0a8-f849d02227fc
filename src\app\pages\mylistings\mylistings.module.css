/* Page Wrapper */
.pageWrapper {
    min-height: 100vh;
    background-color: #f8fafc;
}

/* Main Content */
.mainContent {
    padding: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
}

.contentWrapper {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Mobile Navigation */
.mobileNavContainer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.mobileTabGroup {
    display: flex;
    background-color: #e2e8f0;
    border-radius: 0.75rem;
    padding: 0.25rem;
    overflow-x: auto;
}

.mobileTab {
    flex: 1;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: fit-content;
}

.mobileTab:hover {
    color: #334155;
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.mobileTab:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.mobileTabActive {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #1e293b;
    box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.mobileActionButtons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Desktop Navigation */
.desktopNavContainer {
    display: none;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #ffffff;
    border-radius: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.tabGroup {
    display: flex;
    gap: 0.5rem;
}

.tab {
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab:hover {
    color: #334155;
    border-color: #cbd5e1;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    transform: translateY(-1px);
}

.tab:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.tabActive {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    color: #ffffff;
    border-color: #3b82f6;
    box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.4), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.actionButtonGroup {
    display: flex;
    gap: 0.75rem;
}

.actionButton {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.actionButton:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #3b82f6;
}

.disableButton {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px 0 rgba(239, 68, 68, 0.3);
}

.disableButton:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    box-shadow: 0 6px 20px 0 rgba(239, 68, 68, 0.4);
    transform: translateY(-2px);
}

.enableButton {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px 0 rgba(16, 185, 129, 0.3);
}

.enableButton:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%);
    box-shadow: 0 6px 20px 0 rgba(16, 185, 129, 0.4);
    transform: translateY(-2px);
}


/* Filter Section */
.filterSection {
    margin-bottom: 2rem;
}

.filterCard {
    background-color: #ffffff;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
}

.filterTitle {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 1.5rem 0;
}

.filterForm {
    width: 100%;
}

.filterRow {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.inputGroup {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.inputLabel {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.selectInput,
.textInput {
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: #374151;
    background-color: #ffffff;
    transition: all 0.2s ease;
    min-height: 0.75rem;
}

.selectInput:focus,
.textInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.selectInput:hover,
.textInput:hover {
    border-color: #9ca3af;
}

.inputHelp {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.searchButton {
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    color: #ffffff;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 2.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    min-width: 120px;
    box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.searchButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.searchButton:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e40af 100%);
    box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
}

.searchButton:hover:not(:disabled)::before {
    left: 100%;
}

.searchButton:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #3b82f6;
}

.searchButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.buttonSpinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Listings Section */
.listingsSection {
    margin-bottom: 2rem;
}

.loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4rem 0;
}

.listingsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Empty State */
.emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    background-color: #ffffff;
    border-radius: 1rem;
    border: 2px dashed #e2e8f0;
}

.emptyStateIcon {
    color: #9ca3af;
    margin-bottom: 1rem;
}

.emptyStateTitle {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 0.5rem 0;
}

.emptyStateDescription {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0 0 1.5rem 0;
    max-width: 400px;
    line-height: 1.5;
}

.clearFiltersButton {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    color: #ffffff;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.clearFiltersButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.clearFiltersButton:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e40af 100%);
    box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
}

.clearFiltersButton:hover::before {
    left: 100%;
}

.clearFiltersButton:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #3b82f6;
}

/* Pagination */
.paginationWrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2rem 0;
    padding: 1rem;
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.paginationContainer {
    flex: 1;
    display: flex;
    justify-content: center;
}

.paginationList {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.paginationItem {
    margin: 0;
}

.paginationLink {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.paginationLink:hover {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    transform: translateY(-1px);
}

.paginationActive .paginationLink {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #ffffff;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.3);
}

.paginationNav {
    margin: 0;
}

.paginationNavLink {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;
}

.paginationNavLink:hover {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.paginationArrow {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.paginationBreak {
    margin: 0;
}

.paginationBreakLink {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    color: #6b7280;
    text-decoration: none;
}

.paginationInfo {
    display: flex;
    align-items: center;
}

.resultsCount {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Responsive Design */
@media (min-width: 768px) {
    .mobileNavContainer {
        display: none;
    }

    .desktopNavContainer {
        display: flex;
    }

    .mainContent {
        padding: 2rem;
    }

    .filterRow {
        grid-template-columns: 1fr 1fr 1fr auto;
    }

    .listingsGrid {
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    }
}

@media (min-width: 1024px) {
    .filterRow {
        grid-template-columns: 1fr 1fr 1fr auto;
    }

    .listingsGrid {
        grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 767px) {
    .paginationWrapper {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .paginationContainer {
        order: 2;
    }

    .paginationInfo {
        order: 1;
        justify-content: center;
    }

    .paginationList {
        justify-content: center;
        flex-wrap: wrap;
    }

    .paginationNavLink {
        padding: 0.5rem;
        font-size: 0.75rem;
    }

    .paginationLink {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }
}