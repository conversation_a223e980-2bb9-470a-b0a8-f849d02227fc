"use client";
import React, { useState, useEffect, useRef } from "react";
import styles from "./remFunds.module.css";
import Layout from "../../components/Layout/page";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { Tooltip } from "react-tooltip";
import { useWebsocketContext } from "@/app/context/AuthContext";
import RemflowWithdrawModal from "../../components/RequestWithdrawRemfunds/page";

const page = () => {
  const loadOnceRef = useRef(false);

  const { connection, messageHistory, sendMessage } = useWebsocketContext();
  if (connection.current !== null && loadOnceRef.current == false) {
    loadOnceRef.current = true;
  }

  const router = useRouter();
  const authTokenRef = useRef(null);
  const [depositByCrpto, setDepositByCrpto] = useState(false);
  const [balance, setBalance] = useState([]);
  const [walletAddressTRX, setWalletAddressTRX] = useState("");
  const [walletAddressETH, setWalletAddressETH] = useState("");
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [showAddWalletModal, setShowAddWalletModal] = useState(false);
  const [showSavedWalletModal, setShowSavedWalletModal] = useState(false);
  const [showTranModal, setShowTranModal] = useState(false);
  const [externalWallet, setExternalWallet] = useState("");
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [selectedNetworkCreateWallet, setSelectedNetworkCreateWallet] =
    useState("");
  const [copied, setCopied] = useState(false);
  const [savedExternalWalletsArray, setSavedExternalWalletsArray] = useState(
    []
  );
  const [showTranArray, setShowTranArray] = useState("");

  const customId = "custom-id-yes";
  const handleCopy = () => {
    setCopied(true);
    toast.success("Wallet Address Copied");
  };
  const handleClick = () => {
    setDepositByCrpto(false);
  };
  const handleClickWithdraw = () => {
    setShowRequestModal(!showRequestModal);
    setShowAddWalletModal(false);
    setShowSavedWalletModal(false);
    setShowTranModal(false);
  };
  const handleClickAddWallet = () => {
    setShowAddWalletModal(!showAddWalletModal);
    setShowRequestModal(false);
    setShowSavedWalletModal(false);
    setShowTranModal(false);
  };
  const handleClickSavedWallet = () => {
    setShowSavedWalletModal(!showSavedWalletModal);
    setShowRequestModal(false);
    setShowAddWalletModal(false);
    setShowTranModal(false);
  };
  const handleClickTransaction = () => {
    setShowTranModal(!showTranModal);
    setShowSavedWalletModal(false);
    setShowRequestModal(false);
    setShowAddWalletModal(false);
  };
  const handleCloseAddWalletModal = () => {
    setShowTranModal(false);
    setShowSavedWalletModal(false);
    setShowRequestModal(false);
    setShowAddWalletModal(false);
  };
  const handleSavedClose = () => {
    setShowTranModal(false);
    setShowSavedWalletModal(false);
  };
  const handleSavedCloseTrans = () => {
    setShowTranModal(false);
  };
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }

  const fetchTransactionHistory = async () => {
    try {
      const res = await customFetchWithToken.get("/deposit-history/");

      setShowTranArray(res.data.data.wallet_address);
    } catch (error) {
      setShowTranArray(error.response.data.message);
      console.log(error);
    }
  };
  const fetchWalletAddress = async () => {
    try {
      const res = await customFetchWithToken.get("/get-binace-walletlist/");

      setWalletAddressETH(res.data.data[0][0].address);
      setWalletAddressTRX(res.data.data[1][0].address);
    } catch (error) {
      console.log(error);
    }
  };
  const getWalletBalance = async () => {
    try {
      const res = await customFetchWithToken.get("/get-wallet-balance/");

      setBalance(res.data.data[0]);
    } catch (error) {
      console.log(error);
      toast.error(error.response.data.message);
    }
  };
  const handleCreateWalletAddress = async (network) => {
    const X_MBX_APIKEY = process.env.NEXT_XMBXAPIKEY;
    const headers = {
      "X-MBX-APIKEY": X_MBX_APIKEY,
    };
    try {
      const res = await customFetchWithToken.get(
        `/create-wallet/?coin=USDT&network=${network}`,
        { headers }
      );

      // walletAddress(res)
      toast.success(res.data.message);
    } catch (error) {
      console.log(error);
      toast.error(error.response.data.message);
    }
  };

  const getExternalWallets = async () => {
    try {
      const res = await customFetchWithToken.get("/get-external-wallet/");

      setSavedExternalWalletsArray(res.data.data);
    } catch (error) {
      console.log(error);
    }
  };
  const handleSavedWalletDelete = async (id) => {
    const selectedWalletToDelete = {
      wallet_address: id,
    };

    try {
      const res = await customFetchWithToken.delete(
        "/delete-external-wallet/",
        {
          data: selectedWalletToDelete,
        }
      );

      toast.success(res.data.message);
    } catch (error) {
      console.log(error);
    }
  };

  const handleExternalWalletAddresses = (e) => {
    const ExtwalletAddress = e.target.value;
    const inputValue = ExtwalletAddress.replace(/[^a-zA-Z0-9]/g, "");
    setExternalWallet(inputValue);
  };
  const addExternalWalletAddress = async () => {
    const trxRegex = /^T[A-Za-z1-9]{33}$/;
    const ercRegex = /^(0x)[0-9a-fA-F]{40}$/;

    if (selectedNetwork === "TRX" && !trxRegex.test(externalWallet.trim())) {
      toast.error("Please enter a valid TRX wallet address");
      return;
    } else if (
      selectedNetwork === "USDT" &&
      !ercRegex.test(externalWallet.trim())
    ) {
      toast.error("Please enter a valid ERC wallet address");
      return;
    }

    const Data = [
      {
        networks: selectedNetwork,
        wallet_addresses: externalWallet.trim(),
      },
    ];

    try {
      const res = await customFetchWithToken.post(
        "/add-external-wallet/",
        Data
      );

      toast.success(res.data.message);
    } catch (error) {
      console.log(error);
    }
  };

  // useEffect(() => {
  //   fetchWalletBalance();
  // }, []);
  useEffect(() => {
    fetchWalletAddress();
  }, []);
  useEffect(() => {
    getWalletBalance();
  }, []);
  useEffect(() => {
    getExternalWallets();
  }, []);
  useEffect(() => {
    fetchTransactionHistory();
  }, []);

  return (
    <>
      <div>
        <Layout title="Remflow Funds">
          <div className={styles.pageContainer}>
            {/* Header Section */}
            <div className={styles.pageHeader}>
              <div className={styles.headerContent}>
                <h1 className={styles.pageTitle}>Remflow Funds</h1>
                <p className={styles.pageSubtitle}>
                  Manage your deposits, withdrawals, and wallet addresses
                </p>
              </div>
            </div>

            {/* Balance Card */}
            <div className={styles.balanceCard}>
              <div className={styles.balanceHeader}>
                <div className={styles.balanceIcon}>💰</div>
                <div className={styles.balanceInfo}>
                  <h2 className={styles.balanceTitle}>Account Balance</h2>
                  <div className={styles.balanceAmount}>
                    {balance.asset ? (
                      <>
                        <span className={styles.amount}>
                          {balance.free ? Number(balance.free).toFixed(2) : "0.00"}
                        </span>
                        <span className={styles.currency}>{balance.asset}</span>
                      </>
                    ) : (
                      <>
                        <span className={styles.amount}>0.00</span>
                        <span className={styles.currency}>USDT</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className={styles.balanceDescription}>
                In order to trade on Remflow, you need to make a deposit equivalent to 100% of the trade value.
                We will deduct from your Remflow wallet balance when the trade is completed.
              </div>
            </div>

            {/* Action Buttons */}
            <div className={styles.actionGrid}>
              <button
                className={`${styles.actionButton} ${styles.primaryAction}`}
                onClick={handleClickWithdraw}
              >
                <div className={styles.actionIcon}>🚀</div>
                <div className={styles.actionContent}>
                  <span className={styles.actionTitle}>Request Withdraw</span>
                  <span className={styles.actionDescription}>Withdraw funds to external wallet</span>
                </div>
              </button>

              <button
                className={`${styles.actionButton} ${styles.secondaryAction}`}
                onClick={handleClickTransaction}
              >
                <div className={styles.actionIcon}>📊</div>
                <div className={styles.actionContent}>
                  <span className={styles.actionTitle}>Transaction History</span>
                  <span className={styles.actionDescription}>View your transaction records</span>
                </div>
              </button>

              <button
                className={`${styles.actionButton} ${styles.tertiaryAction}`}
                onClick={handleClickSavedWallet}
              >
                <div className={styles.actionIcon}>💼</div>
                <div className={styles.actionContent}>
                  <span className={styles.actionTitle}>Saved Wallets</span>
                  <span className={styles.actionDescription}>Manage your external wallets</span>
                </div>
              </button>

              <button
                className={`${styles.actionButton} ${styles.quaternaryAction}`}
                onClick={handleClickAddWallet}
              >
                <div className={styles.actionIcon}>➕</div>
                <div className={styles.actionContent}>
                  <span className={styles.actionTitle}>Add Wallet</span>
                  <span className={styles.actionDescription}>Add new external wallet</span>
                </div>
              </button>
            </div>
            {/* Wallet Addresses Section */}
            <div className={styles.walletsSection}>
              <h2 className={styles.sectionTitle}>Your Wallet Addresses</h2>
              <div className={styles.walletsGrid}>
                {/* Ethereum Wallet */}
                <div className={styles.walletCard}>
                  <div className={styles.walletHeader}>
                    <div className={styles.walletIcon}>🔷</div>
                    <div className={styles.walletInfo}>
                      <h3 className={styles.walletTitle}>Ethereum Wallet</h3>
                      <p className={styles.walletNetwork}>ERC-20 Network</p>
                    </div>
                  </div>
                  {walletAddressETH && walletAddressETH.length !== 0 ? (
                    <div className={styles.walletAddressContainer}>
                      <div className={styles.addressDisplay}>
                        <span className={styles.addressText}>{walletAddressETH}</span>
                        <CopyToClipboard
                          text={walletAddressETH}
                          onCopy={handleCopy}
                        >
                          <button className={styles.copyButton} title="Copy Address">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                              <path d="m5,15H4a2,2 0 0,1-2-2V4a2,2 0 0,1,2-2H13a2,2 0 0,1,2,2v1"/>
                            </svg>
                          </button>
                        </CopyToClipboard>
                      </div>
                    </div>
                  ) : (
                    <div className={styles.generateWalletContainer}>
                      <p className={styles.noWalletText}>No wallet address generated</p>
                      <button
                        className={styles.generateButton}
                        onClick={() => handleCreateWalletAddress("BSC")}
                      >
                        <span className={styles.generateIcon}>🔗</span>
                        Generate ERC Wallet
                      </button>
                    </div>
                  )}
                </div>

                {/* Tron Wallet */}
                <div className={styles.walletCard}>
                  <div className={styles.walletHeader}>
                    <div className={styles.walletIcon}>🔴</div>
                    <div className={styles.walletInfo}>
                      <h3 className={styles.walletTitle}>Tron Wallet</h3>
                      <p className={styles.walletNetwork}>TRC-20 Network</p>
                    </div>
                  </div>
                  {walletAddressTRX && walletAddressTRX.length !== 0 ? (
                    <div className={styles.walletAddressContainer}>
                      <div className={styles.addressDisplay}>
                        <span className={styles.addressText}>{walletAddressTRX}</span>
                        <CopyToClipboard
                          text={walletAddressTRX}
                          onCopy={handleCopy}
                        >
                          <button className={styles.copyButton} title="Copy Address">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                              <path d="m5,15H4a2,2 0 0,1-2-2V4a2,2 0 0,1,2-2H13a2,2 0 0,1,2,2v1"/>
                            </svg>
                          </button>
                        </CopyToClipboard>
                      </div>
                    </div>
                  ) : (
                    <div className={styles.generateWalletContainer}>
                      <p className={styles.noWalletText}>No wallet address generated</p>
                      <button
                        className={styles.generateButton}
                        onClick={() => handleCreateWalletAddress("TRX")}
                      >
                        <span className={styles.generateIcon}>🔗</span>
                        Generate TRC Wallet
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Modals */}
            {showRequestModal && (
              <RemflowWithdrawModal
                walletAddress={walletAddressETH || walletAddressTRX}
                onClose={() => setShowRequestModal(false)}
              />
            )}

            {showAddWalletModal && (
              <div className={styles.modernModal}>
                <div className={styles.modernModalContent}>
                  <div className={styles.modalHeader}>
                    <h2 className={styles.modalTitle}>Add External Wallet</h2>
                    <button
                      className={styles.modalCloseButton}
                      onClick={handleCloseAddWalletModal}
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>

                  <div className={styles.modalBody}>
                    <div className={styles.formGroup}>
                      <label className={styles.formLabel}>Network</label>
                      <select
                        className={styles.modernSelect}
                        onChange={(e) => setSelectedNetwork(e.target.value)}
                        value={selectedNetwork}
                      >
                        <option value="">Select Network</option>
                        <option value="USDT">ERC-20 (Ethereum)</option>
                        <option value="TRX">TRC-20 (Tron)</option>
                      </select>
                    </div>

                    <div className={styles.formGroup}>
                      <label className={styles.formLabel}>Wallet Address</label>
                      <input
                        type="text"
                        className={styles.modernInput}
                        placeholder="Enter wallet address"
                        maxLength={260}
                        value={externalWallet}
                        onChange={handleExternalWalletAddresses}
                      />
                    </div>
                  </div>

                  <div className={styles.modalFooter}>
                    <button
                      className={styles.modalCancelButton}
                      onClick={handleCloseAddWalletModal}
                    >
                      Cancel
                    </button>
                    <button
                      className={styles.modalSaveButton}
                      onClick={addExternalWalletAddress}
                    >
                      Add Wallet
                    </button>
                  </div>
                </div>
              </div>
            )}
            {showSavedWalletModal && (
              <div className={styles.modernModal}>
                <div className={styles.modernModalContent}>
                  <div className={styles.modalHeader}>
                    <h2 className={styles.modalTitle}>Saved Wallets</h2>
                    <button
                      className={styles.modalCloseButton}
                      onClick={handleSavedClose}
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>

                  <div className={styles.modalBody}>
                    {savedExternalWalletsArray.length > 0 ? (
                      <div className={styles.walletsList}>
                        {savedExternalWalletsArray.map((wallet, index) => (
                          <div key={index} className={styles.savedWalletItem}>
                            <div className={styles.walletItemHeader}>
                              <div className={styles.walletItemIcon}>
                                {wallet.network === 'TRX' ? '🔴' : '🔷'}
                              </div>
                              <div className={styles.walletItemInfo}>
                                <span className={styles.walletItemNetwork}>
                                  {wallet.network === 'TRX' ? 'TRC-20' : 'ERC-20'}
                                </span>
                                <span className={styles.walletItemAddress}>
                                  {wallet.wallet_address.slice(0, 8)}...{wallet.wallet_address.slice(-8)}
                                </span>
                              </div>
                            </div>
                            <div className={styles.walletItemActions}>
                              <CopyToClipboard
                                text={wallet.wallet_address}
                                onCopy={handleCopy}
                              >
                                <button className={styles.walletActionButton} title="Copy Address">
                                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                    <path d="m5,15H4a2,2 0 0,1-2-2V4a2,2 0 0,1,2-2H13a2,2 0 0,1,2,2v1"/>
                                  </svg>
                                </button>
                              </CopyToClipboard>
                              <button
                                className={`${styles.walletActionButton} ${styles.deleteAction}`}
                                onClick={() => handleSavedWalletDelete(wallet.wallet_address)}
                                title="Delete Wallet"
                              >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <polyline points="3,6 5,6 21,6"/>
                                  <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                </svg>
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className={styles.emptyState}>
                        <div className={styles.emptyIcon}>💼</div>
                        <h3 className={styles.emptyTitle}>No Saved Wallets</h3>
                        <p className={styles.emptyDescription}>
                          You haven't added any external wallets yet.
                        </p>
                        <button
                          className={styles.emptyAction}
                          onClick={() => {
                            handleSavedClose();
                            handleClickAddWallet();
                          }}
                        >
                          Add Your First Wallet
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {showTranModal && (
              <div className={styles.modernModal}>
                <div className={styles.modernModalContent}>
                  <div className={styles.modalHeader}>
                    <h2 className={styles.modalTitle}>Transaction History</h2>
                    <button
                      className={styles.modalCloseButton}
                      onClick={handleSavedCloseTrans}
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>

                  <div className={styles.modalBody}>
                    {showTranArray && showTranArray.length > 0 ? (
                      <div className={styles.transactionsList}>
                        {showTranArray}
                      </div>
                    ) : (
                      <div className={styles.emptyState}>
                        <div className={styles.emptyIcon}>📊</div>
                        <h3 className={styles.emptyTitle}>No Transactions</h3>
                        <p className={styles.emptyDescription}>
                          No transaction history available at the moment.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </Layout>
      </div>

      <Tooltip
        id="my-tooltip"
        style={{
          color: "#fff",
          fontSize: "12px",
        }}
      />
      <ToastContainer />
    </>
  );
};

export default page;
