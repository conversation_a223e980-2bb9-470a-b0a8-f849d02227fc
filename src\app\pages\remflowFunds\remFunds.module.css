@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Page Container */
.pageContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* Page Header */
.pageHeader {
    text-align: center;
    margin-bottom: 32px;
    padding: 32px 0;
}

.headerContent {
    max-width: 600px;
    margin: 0 auto;
}

.pageTitle {
    font-size: 36px;
    font-weight: 800;
    color: #1e293b;
    margin: 0 0 12px 0;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pageSubtitle {
    font-size: 18px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
    line-height: 1.6;
}

/* Balance Card */
.balanceCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 32px;
    margin-bottom: 32px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.balanceCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    border-radius: 24px 24px 0 0;
}

.balanceHeader {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.balanceIcon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.balanceInfo {
    flex: 1;
}

.balanceTitle {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
}

.balanceAmount {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.amount {
    font-size: 32px;
    font-weight: 800;
    color: #059669;
    line-height: 1;
}

.currency {
    font-size: 18px;
    font-weight: 600;
    color: #64748b;
    background: rgba(16, 185, 129, 0.1);
    padding: 4px 12px;
    border-radius: 8px;
}

.balanceDescription {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
    background: rgba(249, 250, 251, 0.8);
    padding: 16px;
    border-radius: 12px;
    border-left: 4px solid #10b981;
}

/* Action Grid */
.actionGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.actionButton {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    position: relative;
    overflow: hidden;
}

.actionButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.actionButton:hover::before {
    left: 100%;
}

.actionButton:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.primaryAction {
    border-top: 4px solid #4f46e5;
}

.primaryAction .actionIcon {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.secondaryAction {
    border-top: 4px solid #10b981;
}

.secondaryAction .actionIcon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.tertiaryAction {
    border-top: 4px solid #f59e0b;
}

.tertiaryAction .actionIcon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

.quaternaryAction {
    border-top: 4px solid #ec4899;
}

.quaternaryAction .actionIcon {
    background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
    box-shadow: 0 8px 20px rgba(236, 72, 153, 0.3);
}

.actionIcon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.actionContent {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.actionTitle {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.actionDescription {
    font-size: 13px;
    color: #64748b;
    margin: 0;
    line-height: 1.4;
}

/* Wallets Section */
.walletsSection {
    margin-bottom: 32px;
}

.sectionTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 24px 0;
    text-align: center;
}

.walletsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

.walletCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.walletCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

.walletCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.walletHeader {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.walletIcon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.walletInfo {
    flex: 1;
}

.walletTitle {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 4px 0;
}

.walletNetwork {
    font-size: 13px;
    color: #64748b;
    margin: 0;
}

.walletAddressContainer {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 16px;
}

.addressDisplay {
    display: flex;
    align-items: center;
    gap: 12px;
}

.addressText {
    flex: 1;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    color: #475569;
    font-weight: 500;
    word-break: break-all;
    line-height: 1.4;
}

.copyButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    flex-shrink: 0;
}

.copyButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

.generateWalletContainer {
    text-align: center;
    padding: 24px 16px;
}

.noWalletText {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 16px 0;
}

.generateButton {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.generateButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
}

.generateIcon {
    font-size: 16px;
}

/* Modern Modal Styles */
.modernModal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modernModalContent {
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    overflow-y: auto;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    animation: slideIn 0.3s ease-out;
    position: relative;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modernModalContent::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    border-radius: 24px 24px 0 0;
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    margin-bottom: 24px;
}

.modalTitle {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.modalCloseButton {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #64748b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.modalCloseButton:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

.modalBody {
    padding: 0 24px 24px;
}

.modalFooter {
    display: flex;
    gap: 12px;
    padding: 24px;
    background: rgba(248, 250, 252, 0.5);
    border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.modalCancelButton {
    flex: 1;
    padding: 14px 24px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    color: #64748b;
    font-size: 16px;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modalCancelButton:hover {
    border-color: #cbd5e1;
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modalSaveButton {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 24px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.modalSaveButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(79, 70, 229, 0.4);
}

/* Form Elements */
.formGroup {
    margin-bottom: 20px;
}

.formLabel {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.modernSelect,
.modernInput {
    width: 100%;
    padding: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
}

.modernSelect:focus,
.modernInput:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: white;
}

.modernSelect {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Saved Wallets List */
.walletsList {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.savedWalletItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.savedWalletItem:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(79, 70, 229, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.walletItemHeader {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.walletItemIcon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.walletItemInfo {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.walletItemNetwork {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.walletItemAddress {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    color: #475569;
    font-weight: 500;
}

.walletItemActions {
    display: flex;
    gap: 8px;
}

.walletActionButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.walletActionButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(99, 102, 241, 0.4);
}

.deleteAction {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3);
}

.deleteAction:hover {
    box-shadow: 0 4px 10px rgba(239, 68, 68, 0.4);
}

/* Empty State */
.emptyState {
    text-align: center;
    padding: 48px 24px;
}

.emptyIcon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.emptyTitle {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
}

.emptyDescription {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 24px 0;
    line-height: 1.5;
}

.emptyAction {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.emptyAction:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

/* Transactions List */
.transactionsList {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pageContainer {
        padding: 16px;
    }

    .pageTitle {
        font-size: 28px;
    }

    .pageSubtitle {
        font-size: 16px;
    }

    .balanceCard {
        padding: 24px 20px;
        border-radius: 20px;
    }

    .balanceHeader {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .amount {
        font-size: 28px;
    }

    .actionGrid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .actionButton {
        padding: 20px;
        border-radius: 16px;
    }

    .walletsGrid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .walletCard {
        padding: 20px;
        border-radius: 16px;
    }

    .modernModalContent {
        max-width: 100%;
        border-radius: 20px;
        margin: 0 8px;
    }

    .modalHeader {
        padding: 20px 20px 0;
    }

    .modalBody {
        padding: 0 20px 20px;
    }

    .modalFooter {
        padding: 20px;
        flex-direction: column;
    }

    .savedWalletItem {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .walletItemActions {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .pageContainer {
        padding: 12px;
    }

    .pageHeader {
        padding: 24px 0;
    }

    .pageTitle {
        font-size: 24px;
    }

    .pageSubtitle {
        font-size: 14px;
    }

    .balanceCard {
        padding: 20px 16px;
        border-radius: 16px;
    }

    .amount {
        font-size: 24px;
    }

    .currency {
        font-size: 16px;
    }

    .actionButton {
        padding: 16px;
        gap: 12px;
    }

    .actionIcon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .actionTitle {
        font-size: 15px;
    }

    .actionDescription {
        font-size: 12px;
    }

    .walletCard {
        padding: 16px;
    }

    .addressText {
        font-size: 12px;
    }

    .modernModalContent {
        border-radius: 16px;
    }

    .modalHeader {
        padding: 16px 16px 0;
    }

    .modalTitle {
        font-size: 18px;
    }

    .modalBody {
        padding: 0 16px 16px;
    }

    .modalFooter {
        padding: 16px;
    }
}

/* Focus Styles for Accessibility */
.actionButton:focus-visible,
.copyButton:focus-visible,
.generateButton:focus-visible,
.modalCloseButton:focus-visible,
.modalCancelButton:focus-visible,
.modalSaveButton:focus-visible,
.walletActionButton:focus-visible,
.emptyAction:focus-visible,
.modernSelect:focus-visible,
.modernInput:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .pageContainer {
        background: white;
    }

    .balanceCard,
    .actionButton,
    .walletCard,
    .modernModalContent {
        background: white;
        border: 2px solid #000;
    }

    .copyButton,
    .generateButton,
    .modalSaveButton,
    .walletActionButton,
    .emptyAction {
        background: #000;
        border: 2px solid #000;
    }

    .modernSelect,
    .modernInput {
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .actionButton,
    .walletCard,
    .copyButton,
    .generateButton,
    .modernModalContent,
    .savedWalletItem,
    .walletActionButton,
    .emptyAction {
        animation: none;
        transition: none;
    }

    .actionButton:hover,
    .walletCard:hover,
    .copyButton:hover,
    .generateButton:hover,
    .savedWalletItem:hover,
    .walletActionButton:hover,
    .emptyAction:hover {
        transform: none;
    }
}

/* need to remove later */

.logoArea {
    height: 20%;
    width: 100%;
    background-color: #4153ed;
    border-radius: 15px 15px 15px 0px;
    display: flex;
    flex-direction: column;
}

.logo {
    margin-top: 5px;
}

.profileBar {
    margin-top: 25px;
}

.profileBarContainer {
    background-color: #4f535a;
    width: 80%;
    height: 35px;
    margin: auto;
    border-radius: 100px;
    background: rgba(255, 255, 255, 0.17);
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.profileImg {
    margin-top: 4px;
    margin-left: 15px;
    margin-right: 5px;
}

.profileName {
    color: #fff;
    font-family: Poppins;
    font-size: 11px;
    font-weight: 500;
}

.profileDropDown {
    margin-left: auto;
    margin-right: 10px;
}

.logoContainer {
    /* width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: end; */
}

.logo {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.profileBar {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50%;
}

.sidebar {
    width: 100%;
    height: auto;
    border-radius: 15px;
    background: #fff;
}

.pages {
    margin-top: 30px;

    width: 100%;
    height: 100%;
}

.dashboardContainer {
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
    border-bottom: 1px solid #ececec;
}

.historyContainer {
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
}

.logoutContainer {
    /* margin-bottom: auto; */
    width: 100%;
    height: 47px;
    /* background-color: aqua; */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
    /* border-bottom: 1px solid #ECECEC; */
}

.sideIcons {
    margin-left: 20px;
    margin-right: 10px;
    color: #4153ed;
}

.dashboard {
    color: #4153ed;
}

.rightContainer {
    width: 78%;
    height: auto;
    display: flex;

    height: 100vh;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.rightContainerWrapper {
    width: 100%;
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
}

.rightContainerHeader {
    width: 100%;
    height: 94px;
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;

    @media screen and (max-width : 576px) {
        height: auto;
        text-align: center;
    }
}

.rightContainerBody {
    height: 100%;
    padding: 30px;
    border-radius: 20px;
    background: #fff;

    @media screen and (max-width : 576px) {
        padding: 0px;
    }
}

.body {
    height: 100%;
    background-color: #fff;
    width: 100%;
}

.firstformWrapper {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-around;
    margin-bottom: 20px;

    @media screen and (max-width: 576px) {
        flex-direction: column;

    }
}

.header {
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;

    @media screen and (max-width : 576px) {
        font-size: 22px;
    }
}

.para {
    padding-left: 20%;
    padding-right: 20%;
    color: #000;
    text-align: center;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 30px;

    @media screen and (max-width : 576px) {
        padding: 0;
        margin-left: 10px;
        text-align: left;
    }
}

.btns {
    width: 100%;
    margin-top: 50px;
    display: flex;
    justify-content: space-around;
    cursor: pointer;

    @media screen and (max-width : 576px) {
        flex-direction: column;
    }
}

.balance {
    font-size: 22px;
    margin-top: 35px;
    /* background-color: #50cd8871; */
    height: 180px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.cryptoButton {
    cursor: pointer;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    border-radius: 5px;
    background: #E8FFF3;
    outline: none;
    border: none;
    color: #50CD89;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;

    @media screen and (max-width : 576px) {
        margin: 5px 0px;
    }
}

.peerButton {
    display: inline-flex;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    border-radius: 5px;
    background: #FFF8DD;
    color: #FFC700;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    outline: none;
    border: none;
    cursor: pointer;

    @media screen and (max-width : 576px) {
        margin: 5px 0px;
    }
}

/* select network */

.selectNetworkWrapper {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}


.network {
    display: flex;
    width: 211px;
    padding: 25px 20px;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    border-radius: 15px;
    border: 1px solid #D9D9D9;
    background: #FFF;
}

.network1 {
    display: flex;
    width: 60%;
    padding: 15px 20px;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    border-radius: 5px;
    border: 1px solid #D9D9D9;
    background: #FFF;

    @media screen and (max-width : 576px) {
        width: 75%;
    }

}

.createWalletDialogue {
    font-size: 18px;
    font-family: poppins;
    font-weight: 500;
    margin: 15px 0px;
    color: #181819;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.networkSelect {
    display: flex;
    width: 100%;
    padding: 15px 20px;
    flex-direction: column;
    align-items: center;
    margin: 15px 0px;
    border-radius: 5px;
    border: 1px solid #D9D9D9;
    background: #FFF;

    @media screen and (max-width : 576px) {
        width: 75%;
    }

}

.netHead {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;

}

.networks {
    display: flex;
    width: 150px;
    padding: 11px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #F9F9F9;
}

/* select network */
.belowCont {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    /* background-color: #4153ed; */
    width: 100%;


    @media screen and (max-width : 576px) {
        width: 100%
    }
}

.walletHeader {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    width: 100%;
    text-align: center;

    @media screen and (max-width : 576px) {
        text-align: center;
    }
}

.walletAddress {

    justify-content: center;
    width: 70%;
    text-align: center;
    margin-top: 20px;

    @media screen and (max-width : 576px) {
        width: 100%;
    }

}

.walletAddress input {

    justify-content: center;
    width: 70%;
    text-align: center;

    @media screen and (max-width : 576px) {
        width: 100%;
    }

}

.authset_up {
    font-size: 12px;
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    color: blue;
    cursor: pointer;
}

.walletAddress1 {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 25px;
}

.walletAddressNew {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 25px;
}

.walletAddressNew input {
    width: 57%;
    margin: auto;
    display: inline-flex;
    height: 30px;
    padding: 10px;
    justify-content: flex-start;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5px;
    background: #F9F9F9;
    outline: none;
    border: none;


    @media screen and (max-width : 576px) {
        width: 68%;
    }
}

.walletAddress1 input {
    width: 57%;
    margin: auto;
    display: inline-flex;
    height: 30px;
    padding: 10px;
    justify-content: flex-start;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5px;
    background: #F9F9F9;
    outline: none;
    border: none;


    @media screen and (max-width : 576px) {
        width: 68%;
    }
}

.walletAddress input {
    width: 100%;
    display: inline-flex;
    height: 40px;
    padding: 10px;
    justify-content: flex-start;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5px;
    background: #F9F9F9;
    outline: none;
    border: none;

    @media screen and (max-width : 576px) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

.requestWithdrawBtnCont {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 25px;
    cursor: pointer;
}

.withdrawBtn {
    width: 60%;
    margin: auto;
    height: 40px;
    background-color: #4153ED;
    color: #ececec;
    font-size: 14px;
    font-weight: 500;
    font-family: poppins;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.depositBtn {
    display: flex;
    width: 88%;
    height: 40px;
    padding: 10px 50px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #4153ED;
    color: #FFF;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    margin-top: 30px;
    margin-bottom: 30px;
    margin-left: 17px;
    cursor: pointer;

    @media screen and (max-width : 576px) {
        width: auto;
    }
}

/* modal */
.modalContainer {
    border-radius: 5px;
    position: absolute;
    top: 35px;
    width: 95%;
    height: 90vh;
    background-color: #dae0eaf7;

    @media screen and (max-width : 576px) {
        width: 100%;
        top: 10px;
    }
}

.modalWrapper {
    width: 100%;
    height: 100vh;
    overflow-y: scroll;
}

.walletListWrapper {
    margin: 10px 0px;
    width: 80%;
    margin: auto;
    border-bottom: 1px solid #4f535a;


}

.walletAddList {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    font-family: poppins;
    font-weight: 400;
    margin: 10px 0px;

}

.wAdd {
    width: 80%;
    display: flex;

    @media screen and (max-width : 576px) {
        width: 55%;

    }
}

.net {
    width: 20%;
    font-weight: 600;
}

.deleteIcon {
    cursor: pointer;

}

.copy {
    margin-left: 10px;
    cursor: pointer;
}

.savedWalletsCont {
    margin: 20px 0px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.balCont {
    display: flex;
    width: 80%;
    justify-content: center;
    align-items: center;
    font-family: poppins;

}

.asset {
    margin-right: 5px;
}

.free {
    margin-left: 5px;
    color: #000;
}

.WalletsCont {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.walletname {
    font-size: 20px;
    font-family: poppins;
    margin-top: 10px;
}

.walletAdd {
    font-size: 16px;
    font-family: poppins;
    margin-top: 10px;
    font-weight: 400;
    margin-right: 20px;
}

.walletCase {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;

}

.generateWalletBtn {
    background-color: #4153ED;
    border-radius: 5px;
    width: 300px;
    color: #ececec;
    font-family: poppins;
    font-size: 16 px;
    height: 40px;
    border-color: #E8FFF3;
    cursor: pointer;
    margin: 15px 0px;
    font-weight: 700;
}